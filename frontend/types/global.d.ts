// 全局类型声明文件
// 解决Nuxt 3自动导入的TypeScript类型问题

import type { Router } from 'vue-router'
import type { RouteLocationNormalizedLoaded } from 'vue-router'
import type { ComputedRef, Ref } from 'vue'

declare global {
  // Nuxt 3 自动导入的组合函数
  interface PageMeta {
    middleware?: string | string[]
    layout?: string
    auth?: boolean
    [key: string]: any
  }

  const definePageMeta: (meta: PageMeta) => void
  const useHead: (head: any) => void
  const useRoute: () => RouteLocationNormalizedLoaded
  const useRouter: () => Router
  const useNuxtApp: () => any
  const navigateTo: (to: string | any, options?: any) => Promise<void> | void
  const createError: (error: { statusCode: number; statusMessage: string }) => Error
  const useToast: () => any
  const useCookie: (name: string, options?: any) => Ref<any>
  const useState: <T>(key: string, init?: () => T) => Ref<T>
  const useAsyncData: (key: string, handler: () => Promise<any>, options?: any) => any
  const useFetch: (url: string, options?: any) => any
  const useRuntimeConfig: () => any
  const useRequestHeaders: (headers?: string[]) => Record<string, string>
  const useRequestURL: () => URL
  const useLazyFetch: (url: string, options?: any) => any
  const useLazyAsyncData: (key: string, handler: () => Promise<any>, options?: any) => any
  const refreshCookie: (name: string) => void
  const clearNuxtData: (keys?: string | string[]) => void
  const reloadNuxtApp: (options?: any) => void
  const preloadComponents: (components: string | string[]) => Promise<void>
  const prefetchComponents: (components: string | string[]) => Promise<void>
  const preloadRouteComponents: (to: string, options?: any) => Promise<void>
  const onBeforeRouteLeave: (guard: any) => void
  const onBeforeRouteUpdate: (guard: any) => void
  const addRouteMiddleware: (name: string, middleware: any, options?: any) => void
  const setPageLayout: (layout: string) => void
  const useError: () => Ref<any>
  const clearError: (options?: any) => Promise<void>
  const isNuxtError: (error: any) => boolean
  const showError: (error: any) => any
  const useRequestEvent: () => any
  const useRequestFetch: () => any
  const setResponseStatus: (code: number, message?: string) => void
  const setResponseHeader: (name: string, value: string) => void
  const setResponseHeaders: (headers: Record<string, string>) => void
  const appendResponseHeader: (name: string, value: string) => void
  const getResponseHeader: (name: string) => string | undefined
  const getResponseHeaders: () => Record<string, string>
  const removeResponseHeader: (name: string) => void
  const clearResponseHeaders: () => void
  const isPrerendered: () => boolean
  const loadPayload: (url: string, options?: any) => Promise<any>
  const preloadPayload: (url: string, options?: any) => Promise<void>
  const definePayloadReducer: (name: string, reduce: (data: any) => any) => void
  const definePayloadReviver: (name: string, revive: (data: any) => any) => void

  // Vue 3 自动导入的组合函数
  const ref: typeof import('vue').ref
  const reactive: typeof import('vue').reactive
  const computed: typeof import('vue').computed
  const readonly: typeof import('vue').readonly
  const watch: typeof import('vue').watch
  const watchEffect: typeof import('vue').watchEffect
  const onMounted: typeof import('vue').onMounted
  const onUnmounted: typeof import('vue').onUnmounted
  const onUpdated: typeof import('vue').onUpdated
  const onBeforeMount: typeof import('vue').onBeforeMount
  const onBeforeUnmount: typeof import('vue').onBeforeUnmount
  const onBeforeUpdate: typeof import('vue').onBeforeUpdate
  const onActivated: typeof import('vue').onActivated
  const onDeactivated: typeof import('vue').onDeactivated
  const onErrorCaptured: typeof import('vue').onErrorCaptured
  const onRenderTracked: typeof import('vue').onRenderTracked
  const onRenderTriggered: typeof import('vue').onRenderTriggered
  const onServerPrefetch: typeof import('vue').onServerPrefetch
  const provide: typeof import('vue').provide
  const inject: typeof import('vue').inject
  const nextTick: typeof import('vue').nextTick
  const toRef: typeof import('vue').toRef
  const toRefs: typeof import('vue').toRefs
  const unref: typeof import('vue').unref
  const isRef: typeof import('vue').isRef
  const isReactive: typeof import('vue').isReactive
  const isReadonly: typeof import('vue').isReadonly
  const isProxy: typeof import('vue').isProxy
  const shallowRef: typeof import('vue').shallowRef
  const shallowReactive: typeof import('vue').shallowReactive
  const shallowReadonly: typeof import('vue').shallowReadonly
  const triggerRef: typeof import('vue').triggerRef
  const customRef: typeof import('vue').customRef
  const toRaw: typeof import('vue').toRaw
  const markRaw: typeof import('vue').markRaw
  const effectScope: typeof import('vue').effectScope
  const getCurrentScope: typeof import('vue').getCurrentScope
  const onScopeDispose: typeof import('vue').onScopeDispose

  // 项目自定义的组合函数
  const useAuthStore: () => any
  const useCrud: (options: any) => any
  const useForm: (initialState: any) => any
  const useSearch: (loadFunction: Function) => any

  // 全局组件类型扩展
  interface ComponentCustomProperties {
    $config: {
      public: {
        apiBase: string
        appName: string
        appVersion: string
      }
    }
    $apiClient: (url: string, options?: any) => Promise<any>
    navigateTo: (to: string | any, options?: any) => Promise<void> | void
  }

  // NodeJS 类型支持
  namespace NodeJS {
    interface Timeout {}
    interface Immediate {}
    interface Process {
      env: Record<string, string | undefined>
    }
  }

  // 扩展 Window 接口
  interface Window {
    // 可以在这里添加全局的 window 属性
  }
}

export {}
